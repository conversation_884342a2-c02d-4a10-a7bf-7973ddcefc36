import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def check_kline_gaps(df):
    """
    检查K线数据中的时间间隙
    Args:
        df: K线数据DataFrame
    Returns:
        gaps: 缺失的时间段列表
    """
    print("正在检查K线数据的时间连续性...")
    
    # 转换时间戳为datetime
    df['datetime'] = pd.to_datetime(df['timestamp_sec'], unit='s')
    df_sorted = df.sort_values('timestamp_sec').reset_index(drop=True)
    
    # 检查时间间隙
    gaps = []
    expected_interval = 60  # 1分钟 = 60秒
    
    for i in range(1, len(df_sorted)):
        current_time = df_sorted['timestamp_sec'].iloc[i]
        prev_time = df_sorted['timestamp_sec'].iloc[i-1]
        time_diff = current_time - prev_time
        
        if time_diff > expected_interval:
            # 发现间隙
            gap_minutes = time_diff // 60
            gap_start = datetime.fromtimestamp(prev_time + 60)
            gap_end = datetime.fromtimestamp(current_time - 60)
            
            gaps.append({
                'gap_start': gap_start,
                'gap_end': gap_end,
                'gap_minutes': gap_minutes - 1,  # 减去1因为不包括边界
                'prev_timestamp': prev_time,
                'next_timestamp': current_time,
                'prev_close': df_sorted['close'].iloc[i-1],
                'next_open': df_sorted['open'].iloc[i]
            })
    
    print(f"检查完成，发现 {len(gaps)} 个时间间隙")
    
    if gaps:
        print("\n发现的时间间隙:")
        total_missing = 0
        for i, gap in enumerate(gaps, 1):
            print(f"  {i}. {gap['gap_start'].strftime('%H:%M:%S')} - {gap['gap_end'].strftime('%H:%M:%S')} "
                  f"(缺失 {gap['gap_minutes']} 分钟)")
            total_missing += gap['gap_minutes']
        print(f"\n总计缺失: {total_missing} 分钟的K线数据")
    else:
        print("✓ 未发现时间间隙，K线数据连续完整")
    
    return gaps

def interpolate_missing_klines(df, gaps):
    """
    使用插值方法填补缺失的K线
    Args:
        df: 原始K线数据DataFrame
        gaps: 缺失时间段列表
    Returns:
        完整的K线数据DataFrame
    """
    if not gaps:
        print("无需插值，数据已完整")
        return df
    
    print(f"正在使用插值方法填补 {len(gaps)} 个时间间隙...")
    
    # 创建完整的时间序列
    df_sorted = df.sort_values('timestamp_sec').reset_index(drop=True)
    start_time = df_sorted['timestamp_sec'].min()
    end_time = df_sorted['timestamp_sec'].max()
    
    # 生成完整的分钟时间戳序列
    complete_timestamps = []
    current_timestamp = start_time
    while current_timestamp <= end_time:
        complete_timestamps.append(current_timestamp)
        current_timestamp += 60  # 每分钟
    
    print(f"原始数据: {len(df)} 根K线")
    print(f"完整序列应有: {len(complete_timestamps)} 根K线")
    print(f"需要插值: {len(complete_timestamps) - len(df)} 根K线")
    
    # 创建完整的DataFrame
    complete_df = pd.DataFrame({'timestamp_sec': complete_timestamps})
    
    # 合并原始数据
    merged_df = pd.merge(complete_df, df, on='timestamp_sec', how='left')
    
    # 对缺失的数据进行插值
    interpolated_count = 0
    
    for col in ['open', 'close', 'high', 'low']:
        # 线性插值价格数据
        merged_df[col] = merged_df[col].interpolate(method='linear')
        
    # 对于交易量数据，使用前向填充然后适当缩放
    for col in ['amount', 'vol']:
        # 先用0填充缺失值
        merged_df[col] = merged_df[col].fillna(0)
        
        # 对于连续的缺失段，使用相邻数据的平均值
        for gap in gaps:
            gap_start_ts = int(gap['gap_start'].timestamp())
            gap_end_ts = int(gap['gap_end'].timestamp())
            
            # 找到间隙前后的交易量
            prev_idx = merged_df[merged_df['timestamp_sec'] == gap['prev_timestamp']].index
            next_idx = merged_df[merged_df['timestamp_sec'] == gap['next_timestamp']].index
            
            if len(prev_idx) > 0 and len(next_idx) > 0:
                prev_amount = merged_df.loc[prev_idx[0], 'amount']
                next_amount = merged_df.loc[next_idx[0], 'amount']
                prev_vol = merged_df.loc[prev_idx[0], 'vol']
                next_vol = merged_df.loc[next_idx[0], 'vol']
                
                # 计算平均值并分配给间隙中的K线
                avg_amount = (prev_amount + next_amount) / 2 * 0.1  # 减少到10%，因为是插值数据
                avg_vol = (prev_vol + next_vol) / 2 * 0.1
                
                # 填充间隙
                gap_mask = (merged_df['timestamp_sec'] >= gap_start_ts) & (merged_df['timestamp_sec'] <= gap_end_ts)
                merged_df.loc[gap_mask, 'amount'] = avg_amount
                merged_df.loc[gap_mask, 'vol'] = avg_vol
                
                interpolated_count += gap_mask.sum()
    
    # 确保OHLC逻辑正确
    for i in range(len(merged_df)):
        open_price = merged_df['open'].iloc[i]
        close_price = merged_df['close'].iloc[i]
        high_price = merged_df['high'].iloc[i]
        low_price = merged_df['low'].iloc[i]
        
        # 确保high >= max(open, close) 且 low <= min(open, close)
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)
        
        merged_df.iloc[i, merged_df.columns.get_loc('high')] = corrected_high
        merged_df.iloc[i, merged_df.columns.get_loc('low')] = corrected_low
    
    # 重新排列列顺序
    final_df = merged_df[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()
    
    # 格式化数值
    for col in ['amount', 'vol', 'open', 'close', 'high', 'low']:
        final_df[col] = final_df[col].round(6)
    
    print(f"插值完成，共插值 {interpolated_count} 根K线")
    print(f"最终数据: {len(final_df)} 根K线")
    
    return final_df

def analyze_interpolation_quality(original_df, interpolated_df):
    """分析插值质量"""
    print("\n=== 插值质量分析 ===")
    
    # 标记插值数据
    original_timestamps = set(original_df['timestamp_sec'])
    interpolated_df['is_interpolated'] = ~interpolated_df['timestamp_sec'].isin(original_timestamps)
    
    interpolated_count = interpolated_df['is_interpolated'].sum()
    total_count = len(interpolated_df)
    
    print(f"原始数据: {len(original_df)} 根K线")
    print(f"插值数据: {interpolated_count} 根K线")
    print(f"总数据: {total_count} 根K线")
    print(f"插值比例: {interpolated_count/total_count*100:.1f}%")
    
    # 分析插值数据的价格连续性
    interpolated_rows = interpolated_df[interpolated_df['is_interpolated']]
    if len(interpolated_rows) > 0:
        print(f"\n插值数据统计:")
        print(f"  价格范围: {interpolated_rows['low'].min():.6f} - {interpolated_rows['high'].max():.6f}")
        print(f"  平均交易额: {interpolated_rows['amount'].mean():.2f} USDT")
        print(f"  平均交易量: {interpolated_rows['vol'].mean():.2f} BYB")
        
        # 检查价格跳跃
        price_jumps = []
        for i in range(1, len(interpolated_df)):
            if interpolated_df['is_interpolated'].iloc[i]:
                prev_close = interpolated_df['close'].iloc[i-1]
                current_open = interpolated_df['open'].iloc[i]
                jump = abs(current_open - prev_close) / prev_close
                price_jumps.append(jump)
        
        if price_jumps:
            avg_jump = np.mean(price_jumps)
            max_jump = max(price_jumps)
            print(f"  平均价格跳跃: {avg_jump:.4f} ({avg_jump*100:.2f}%)")
            print(f"  最大价格跳跃: {max_jump:.4f} ({max_jump*100:.2f}%)")
            
            if max_jump < 0.01:
                print("  ✓ 插值质量良好，价格连续性保持良好")
            else:
                print("  ⚠ 存在较大价格跳跃，可能需要进一步优化")

def generate_gap_report(gaps, original_df, final_df):
    """生成间隙分析报告"""
    report_lines = []
    report_lines.append("BYBUSDT K线数据间隙分析报告")
    report_lines.append("=" * 50)
    report_lines.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append("")
    
    report_lines.append("原始数据统计:")
    report_lines.append(f"  K线数量: {len(original_df)} 根")
    start_time = datetime.fromtimestamp(original_df['timestamp_sec'].min())
    end_time = datetime.fromtimestamp(original_df['timestamp_sec'].max())
    report_lines.append(f"  时间范围: {start_time} 到 {end_time}")
    total_minutes = (original_df['timestamp_sec'].max() - original_df['timestamp_sec'].min()) // 60 + 1
    report_lines.append(f"  理论K线数: {total_minutes} 根")
    report_lines.append(f"  缺失K线数: {total_minutes - len(original_df)} 根")
    report_lines.append("")
    
    if gaps:
        report_lines.append(f"发现的时间间隙 ({len(gaps)} 个):")
        total_missing = 0
        for i, gap in enumerate(gaps, 1):
            report_lines.append(f"  {i}. {gap['gap_start'].strftime('%H:%M:%S')} - {gap['gap_end'].strftime('%H:%M:%S')} "
                              f"(缺失 {gap['gap_minutes']} 分钟)")
            total_missing += gap['gap_minutes']
        report_lines.append(f"  总计缺失: {total_missing} 分钟")
    else:
        report_lines.append("未发现时间间隙")
    
    report_lines.append("")
    report_lines.append("插值后数据统计:")
    report_lines.append(f"  K线数量: {len(final_df)} 根")
    report_lines.append(f"  插值K线: {len(final_df) - len(original_df)} 根")
    report_lines.append(f"  数据完整性: {len(final_df) / total_minutes * 100:.1f}%")
    
    # 保存报告
    report_filename = f'kline_gap_analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"\n间隙分析报告已保存为: {report_filename}")
    return report_filename

def main():
    """主函数"""
    try:
        print("="*80)
        print("BYBUSDT K线数据间隙检查和插值修复")
        print("="*80)
        
        # 1. 读取K线数据
        print("正在读取K线数据...")
        df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
        print(f"读取到 {len(df)} 根K线数据")
        
        # 2. 检查时间间隙
        gaps = check_kline_gaps(df)
        
        # 3. 如果有间隙，进行插值
        if gaps:
            final_df = interpolate_missing_klines(df, gaps)
            
            # 4. 分析插值质量
            analyze_interpolation_quality(df, final_df)
            
            # 5. 保存完整数据
            output_filename = 'bybusdt_1min_kline_complete_2025-07-03.csv'
            final_df_output = final_df.drop('is_interpolated', axis=1, errors='ignore')
            final_df_output.to_csv(output_filename, index=False)
            print(f"\n完整K线数据已保存为: {output_filename}")
            
            # 6. 生成报告
            report_file = generate_gap_report(gaps, df, final_df)
            
            print(f"\n=== 处理完成 ===")
            print(f"原始文件: bybusdt_1min_kline_2025-07-03.csv ({len(df)} 根K线)")
            print(f"完整文件: {output_filename} ({len(final_df)} 根K线)")
            print(f"分析报告: {report_file}")
            print(f"插值K线: {len(final_df) - len(df)} 根")
            
        else:
            print("\n✓ K线数据完整，无需插值")
            
            # 仍然生成报告
            report_file = generate_gap_report(gaps, df, df)
            print(f"分析报告: {report_file}")
        
        print("\n🎉 K线数据间隙检查完成！")
        
    except FileNotFoundError:
        print("错误: 未找到文件 'bybusdt_1min_kline_2025-07-03.csv'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
