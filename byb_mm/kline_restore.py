import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def process_bybusdt_trades_to_kline():
    """
    将bybusdt交易记录整理成1分钟K线数据
    数据格式：时间戳(秒) amount交易额 vol交易量 open开盘价 close收盘价 high最高价 low最低价
    """
    print("正在读取Excel文件...")

    # 读取Excel文件
    df = pd.read_excel('用户实时交易单2025-07-03-2025-07-03.xls')
    print(f"总交易记录数: {len(df)}")

    # 筛选bybusdt交易对
    df = df[df['币对'] == 'bybusdt']
    print(f"bybusdt交易记录数: {len(df)}")

    if len(df) == 0:
        print("未找到bybusdt交易记录")
        return

    # 数据预处理
    print("正在处理数据...")

    # 转换时间格式
    df['timestamp'] = pd.to_datetime(df['成交時間'])

    # 转换数值类型 - 处理可能的字符串格式
    df['成交数量'] = pd.to_numeric(df['成交数量'].astype(str).str.replace(' BYB', ''), errors='coerce')
    df['成交价格'] = pd.to_numeric(df['成交价格'].astype(str).str.replace(' USDT', ''), errors='coerce')

    # 计算交易额 (成交数量 * 成交价格)
    df['交易额'] = df['成交数量'] * df['成交价格']

    # 删除无效数据
    df = df.dropna(subset=['成交数量', '成交价格', '交易额'])
    print(f"有效交易记录数: {len(df)}")

    # 按时间排序
    df = df.sort_values('timestamp')

    # 设置时间为索引，并重采样为1分钟
    df.set_index('timestamp', inplace=True)

    print("正在生成1分钟K线数据...")

    # 生成1分钟K线数据
    kline_data = df.resample('1T').agg({
        '成交价格': ['first', 'max', 'min', 'last'],  # 开盘价、最高价、最低价、收盘价
        '交易额': 'sum',                              # 交易额总和
        '成交数量': 'sum'                             # 交易量总和
    }).dropna()

    # 重命名列
    kline_data.columns = ['open', 'high', 'low', 'close', 'amount', 'vol']

    # 重置索引，获取时间戳
    kline_data.reset_index(inplace=True)

    # 转换时间戳为秒
    kline_data['timestamp_sec'] = kline_data['timestamp'].astype('int64') // 10**9

    # 重新排列列的顺序
    result = kline_data[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()

    # 第一步：检测价格异常
    print("第一步：检测价格异常...")
    anomalies = detect_price_anomalies(result, max_change_pct=0.05)

    # 第二步：优化极端跳空
    print("第二步：优化极端跳空...")
    result, gap_optimization_log = optimize_extreme_gaps(result, max_change_pct=0.03)

    # 第三步：应用超严格价格平滑
    print("第三步：应用超严格价格平滑...")
    result = ultra_strict_price_smoothing(result, max_volatility=0.01)

    # 第四步：应用OHLC平滑优化
    print("第四步：应用OHLC平滑优化...")
    result = apply_ohlc_smoothing(result, max_volatility=0.01, smoothing_factor=0.6)  # 限制单根K线波动(high/low-1)不超过1%

    # 第五步：应用影线修复
    print("第五步：应用影线修复...")
    result = shorten_long_shadows(result, max_shadow_ratio=1.0, min_body_ratio=0.001)

    # 第六步：检测和平滑震荡区间
    print("第六步：检测和平滑震荡区间...")
    oscillation_zones = detect_oscillation_patterns(result, window=5, oscillation_threshold=0.005)
    if oscillation_zones:
        result = smooth_oscillation_zones(result, oscillation_zones, smoothing_strength=0.8)

    # 格式化数值显示
    result['amount'] = result['amount'].round(6)
    result['vol'] = result['vol'].round(6)
    result['open'] = result['open'].round(6)
    result['close'] = result['close'].round(6)
    result['high'] = result['high'].round(6)
    result['low'] = result['low'].round(6)

    print(f"生成K线数据条数: {len(result)}")
    print("\n前10条K线数据:")
    print(result.head(10).to_string(index=False))

    # 保存到CSV文件
    output_file = 'bybusdt_1min_kline_2025-07-03.csv'
    result.to_csv(output_file, index=False)
    print(f"\nK线数据已保存到: {output_file}")

    # 显示统计信息
    print(f"\n统计信息:")
    print(f"时间范围: {kline_data['timestamp'].min()} 到 {kline_data['timestamp'].max()}")
    print(f"总交易额: {result['amount'].sum():.2f} USDT")
    print(f"总交易量: {result['vol'].sum():.2f} BYB")
    print(f"价格范围: {result['low'].min():.6f} - {result['high'].max():.6f} USDT")

    return result

def detect_price_anomalies(df, max_change_pct=0.05):
    """
    检测价格异常（来自price_smoothing_optimizer.py）
    Args:
        df: K线数据DataFrame
        max_change_pct: 最大允许的价格变化百分比
    Returns:
        异常列表
    """
    anomalies = []

    print(f"正在检测价格异常，阈值: {max_change_pct*100:.1f}%...")

    for i in range(1, len(df)):
        prev_close = df.iloc[i-1]['close']
        current_open = df.iloc[i]['open']
        current_close = df.iloc[i]['close']
        current_high = df.iloc[i]['high']
        current_low = df.iloc[i]['low']

        # 检测开盘价跳空
        open_gap = abs(current_open - prev_close) / prev_close
        if open_gap > max_change_pct:
            anomalies.append({
                'index': i,
                'type': '开盘跳空',
                'severity': open_gap,
                'prev_close': prev_close,
                'current_open': current_open
            })

        # 检测单根K线内部的极端波动
        if current_high > 0 and current_low > 0:
            intra_volatility = (current_high - current_low) / current_low
            if intra_volatility > max_change_pct * 2:  # 单根K线波动超过阈值的2倍
                anomalies.append({
                    'index': i,
                    'type': '单根K线极端波动',
                    'severity': intra_volatility,
                    'high': current_high,
                    'low': current_low
                })

        # 检测收盘价异常
        close_change = abs(current_close - prev_close) / prev_close
        if close_change > max_change_pct:
            anomalies.append({
                'index': i,
                'type': '收盘价异常',
                'severity': close_change,
                'prev_close': prev_close,
                'current_close': current_close
            })

    print(f"检测到 {len(anomalies)} 个价格异常")
    return anomalies

def optimize_extreme_gaps(df, max_change_pct=0.03):
    """
    优化极端跳空（来自price_smoothing_optimizer.py）
    Args:
        df: K线数据DataFrame
        max_change_pct: 最大允许的价格变化百分比
    Returns:
        优化后的DataFrame和优化日志
    """
    df_optimized = df.copy()
    optimization_log = []

    print(f"正在优化极端跳空，限制: {max_change_pct*100:.1f}%...")

    for i in range(1, len(df_optimized)):
        prev_close = df_optimized.iloc[i-1]['close']
        current_open = df_optimized.iloc[i]['open']

        # 计算跳空幅度
        gap_pct = abs(current_open - prev_close) / prev_close

        if gap_pct > max_change_pct:
            # 限制跳空幅度
            max_change = prev_close * max_change_pct
            if current_open > prev_close:
                # 向上跳空
                optimized_open = prev_close + max_change
            else:
                # 向下跳空
                optimized_open = prev_close - max_change

            # 记录优化信息
            optimization_log.append({
                'index': i,
                'timestamp': df_optimized.iloc[i]['timestamp_sec'],
                'type': '跳空优化',
                'original_open': current_open,
                'optimized_open': optimized_open,
                'gap_reduction': abs(current_open - prev_close) - abs(optimized_open - prev_close)
            })

            # 应用优化
            adjustment_ratio = optimized_open / current_open
            df_optimized.iloc[i, df_optimized.columns.get_loc('open')] = optimized_open
            df_optimized.iloc[i, df_optimized.columns.get_loc('close')] *= adjustment_ratio
            df_optimized.iloc[i, df_optimized.columns.get_loc('high')] *= adjustment_ratio
            df_optimized.iloc[i, df_optimized.columns.get_loc('low')] *= adjustment_ratio

    print(f"优化了 {len(optimization_log)} 个极端跳空")
    return df_optimized, optimization_log

def ultra_strict_price_smoothing(df, max_volatility=0.01):
    """
    超严格价格平滑（来自ultra_strict_kline_optimizer.py）
    Args:
        df: K线数据DataFrame
        max_volatility: 最大允许波动率
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()

    print(f"正在应用超严格价格平滑，波动限制: {max_volatility*100:.1f}%...")

    # 第一步：强力平滑收盘价序列
    smoothing_factor = 0.8  # 非常强的平滑
    smoothed_close = [df_optimized['close'].iloc[0]]

    for i in range(1, len(df_optimized)):
        prev_close = smoothed_close[-1]
        current_close = df_optimized['close'].iloc[i]

        # 限制收盘价变化幅度为0.5%
        max_change = prev_close * 0.005
        if abs(current_close - prev_close) > max_change:
            if current_close > prev_close:
                new_close = prev_close + max_change
            else:
                new_close = prev_close - max_change
        else:
            # 应用强平滑
            new_close = smoothing_factor * prev_close + (1 - smoothing_factor) * current_close

        smoothed_close.append(new_close)

    df_optimized['close'] = smoothed_close

    # 第二步：基于平滑收盘价调整开盘价
    df_optimized.iloc[0, df_optimized.columns.get_loc('open')] = df_optimized['close'].iloc[0]

    for i in range(1, len(df_optimized)):
        prev_close = df_optimized['close'].iloc[i-1]
        current_open = df_optimized['open'].iloc[i]

        # 限制开盘价跳空为0.3%
        max_gap = prev_close * 0.003
        if abs(current_open - prev_close) > max_gap:
            if current_open > prev_close:
                new_open = prev_close + max_gap
            else:
                new_open = prev_close - max_gap
            df_optimized.iloc[i, df_optimized.columns.get_loc('open')] = new_open

    print("超严格价格平滑完成")
    return df_optimized

def apply_ohlc_smoothing(df, max_volatility=0.01, smoothing_factor=0.6):
    """
    对OHLC四个价格同时进行平滑优化，确保 high/low - 1 不超过1%
    Args:
        df: K线数据DataFrame
        max_volatility: 单根K线最大允许波动率，使用high/low-1公式 (默认1%)
        smoothing_factor: 平滑因子 (0-1之间，越大平滑程度越高)
    Returns:
        优化后的DataFrame
    """
    df_smoothed = df.copy()
    optimization_count = 0

    print(f"开始OHLC平滑优化，限制单根K线波动不超过{max_volatility*100:.1f}%...")

    # 第一步：对收盘价进行指数移动平均平滑
    alpha = smoothing_factor
    smoothed_close = [df_smoothed['close'].iloc[0]]

    for i in range(1, len(df_smoothed)):
        # 指数移动平均
        new_close = alpha * df_smoothed['close'].iloc[i] + (1 - alpha) * smoothed_close[-1]
        smoothed_close.append(new_close)

    df_smoothed['close'] = smoothed_close

    # 第二步：基于平滑后的收盘价调整开盘价
    for i in range(1, len(df_smoothed)):
        prev_close = df_smoothed['close'].iloc[i-1]
        current_open = df_smoothed['open'].iloc[i]

        # 限制开盘价跳空幅度
        max_gap = prev_close * max_volatility
        if abs(current_open - prev_close) > max_gap:
            if current_open > prev_close:
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('open')] = prev_close + max_gap
            else:
                df_smoothed.iloc[i, df_smoothed.columns.get_loc('open')] = prev_close - max_gap
            optimization_count += 1

    # 第三步：对开盘价也进行轻度平滑
    smoothed_open = [df_smoothed['open'].iloc[0]]
    for i in range(1, len(df_smoothed)):
        # 使用较小的平滑因子保持开盘价的相对独立性
        new_open = (smoothing_factor * 0.5) * df_smoothed['open'].iloc[i] + (1 - smoothing_factor * 0.5) * smoothed_open[-1]
        smoothed_open.append(new_open)

    df_smoothed['open'] = smoothed_open

    # 第四步：缩短长影线并优化每根K线的高低价，确保波动不超过限制
    for i in range(len(df_smoothed)):
        open_price = df_smoothed['open'].iloc[i]
        close_price = df_smoothed['close'].iloc[i]
        high_price = df_smoothed['high'].iloc[i]
        low_price = df_smoothed['low'].iloc[i]

        # 缩短长影线：限制影线长度不超过实体长度
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2

        # 如果实体很小，使用最小实体大小来计算影线限制
        min_body_size = body_center * 0.001  # 最小实体为价格的0.1%
        effective_body_size = max(body_size, min_body_size)

        # 影线长度限制：不超过实体大小的1倍（即影线≤实体）
        max_shadow_length = effective_body_size * 1.0

        # 缩短上影线
        max_allowed_high = max(open_price, close_price) + max_shadow_length
        if high_price > max_allowed_high:
            high_price = max_allowed_high
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = high_price

        # 缩短下影线
        min_allowed_low = min(open_price, close_price) - max_shadow_length
        if low_price < min_allowed_low:
            low_price = min_allowed_low
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = low_price

        # 计算当前K线的波动率 (high/low - 1)
        if low_price > 0:
            current_volatility = high_price / low_price - 1
        else:
            current_volatility = 0

        if current_volatility > max_volatility:  # 如果波动超过限制
            # 基于high/low-1公式重新计算
            # 目标：high/low - 1 = max_volatility
            # 即：high = low * (1 + max_volatility)

            # 确保高低价包含开盘价和收盘价
            min_high = max(open_price, close_price)
            max_low = min(open_price, close_price)

            # 方法1：以低价为基准调整高价
            target_high_from_low = max_low * (1 + max_volatility)
            if target_high_from_low >= min_high:
                new_low = max_low
                new_high = target_high_from_low
            else:
                # 方法2：以高价为基准调整低价
                new_high = min_high
                new_low = new_high / (1 + max_volatility)

                # 确保低价不高于开盘收盘价的最小值
                if new_low > max_low:
                    # 如果调整后的低价仍然太高，需要平衡调整
                    # 在开盘收盘价范围内找到合适的高低价
                    oc_mid = (open_price + close_price) / 2
                    oc_range = abs(open_price - close_price)

                    # 以开盘收盘价中点为基准，向两边扩展
                    half_target_range = oc_mid * max_volatility / 2
                    new_high = oc_mid + half_target_range
                    new_low = oc_mid - half_target_range

                    # 确保包含开盘收盘价
                    new_high = max(new_high, min_high)
                    new_low = min(new_low, max_low)

            df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = new_high
            df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = new_low
            optimization_count += 1

    # 第五步：最终一致性检查和微调
    for i in range(len(df_smoothed)):
        open_price = df_smoothed['open'].iloc[i]
        close_price = df_smoothed['close'].iloc[i]
        high_price = df_smoothed['high'].iloc[i]
        low_price = df_smoothed['low'].iloc[i]

        # 确保OHLC逻辑正确
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)

        # 最终波动检查 (使用high/low-1公式)
        if corrected_low > 0:
            final_volatility = corrected_high / corrected_low - 1

            if final_volatility > max_volatility:  # 如果仍然超过限制
                # 进行最终调整 - 严格控制在限制范围内
                # 目标：high/low - 1 = max_volatility

                # 确保包含开盘价和收盘价
                min_high = max(open_price, close_price)
                max_low = min(open_price, close_price)

                # 以低价为基准调整
                target_high = max_low * (1 + max_volatility)
                if target_high >= min_high:
                    corrected_low = max_low
                    corrected_high = target_high
                else:
                    # 以高价为基准调整
                    corrected_high = min_high
                    corrected_low = corrected_high / (1 + max_volatility)

                    # 确保低价合理
                    if corrected_low > max_low:
                        # 强制调整：在开盘收盘价范围内
                        corrected_high = min_high
                        corrected_low = max_low

        df_smoothed.iloc[i, df_smoothed.columns.get_loc('high')] = corrected_high
        df_smoothed.iloc[i, df_smoothed.columns.get_loc('low')] = corrected_low

    # 统计优化效果
    original_volatilities = []
    smoothed_volatilities = []

    for i in range(len(df)):
        # 原始数据波动率 (high/low - 1)
        if df['low'].iloc[i] > 0:
            orig_vol = df['high'].iloc[i] / df['low'].iloc[i] - 1
            original_volatilities.append(orig_vol)

        # 平滑后数据波动率 (high/low - 1)
        if df_smoothed['low'].iloc[i] > 0:
            smooth_vol = df_smoothed['high'].iloc[i] / df_smoothed['low'].iloc[i] - 1
            smoothed_volatilities.append(smooth_vol)

    avg_original_vol = np.mean(original_volatilities) if original_volatilities else 0
    avg_smoothed_vol = np.mean(smoothed_volatilities) if smoothed_volatilities else 0
    max_original_vol = max(original_volatilities) if original_volatilities else 0
    max_smoothed_vol = max(smoothed_volatilities) if smoothed_volatilities else 0

    # 统计超过限制的K线数量
    over_limit_original = sum(1 for vol in original_volatilities if vol > max_volatility)
    over_limit_smoothed = sum(1 for vol in smoothed_volatilities if vol > max_volatility)

    print(f"OHLC平滑优化完成 (包含长影线缩短):")
    print(f"  优化操作次数: {optimization_count}")
    print(f"  平均波动率: {avg_original_vol:.4f} → {avg_smoothed_vol:.4f}")
    print(f"  最大波动率: {max_original_vol:.4f} → {max_smoothed_vol:.4f}")
    print(f"  超过{max_volatility*100:.1f}%限制的K线: {over_limit_original} → {over_limit_smoothed}")

    if over_limit_smoothed == 0:
        print(f"✓ 所有K线波动已控制在{max_volatility*100:.1f}%以内，长影线已缩短")
    else:
        print(f"⚠ 仍有{over_limit_smoothed}根K线超过限制，可能需要进一步调整参数")

    return df_smoothed

def shorten_long_shadows(df, max_shadow_ratio=1.0, min_body_ratio=0.001):
    """
    缩短K线的长影线（影线修复）
    Args:
        df: K线数据DataFrame
        max_shadow_ratio: 影线长度相对于实体的最大比例 (默认1倍，即影线≤实体)
        min_body_ratio: 最小实体大小相对于价格的比例 (默认0.1%)
    Returns:
        优化后的DataFrame
    """
    df_optimized = df.copy()
    shadow_shortened_count = 0

    print(f"正在进行影线修复，影线限制为实体的{max_shadow_ratio}倍（影线≤实体）...")

    for i in range(len(df_optimized)):
        open_price = df_optimized['open'].iloc[i]
        close_price = df_optimized['close'].iloc[i]
        high_price = df_optimized['high'].iloc[i]
        low_price = df_optimized['low'].iloc[i]

        # 计算实体大小和中心价格
        body_size = abs(close_price - open_price)
        body_center = (open_price + close_price) / 2

        # 如果实体很小，使用最小实体大小来计算影线限制
        min_body_size = body_center * min_body_ratio
        effective_body_size = max(body_size, min_body_size)

        # 影线长度限制
        max_shadow_length = effective_body_size * max_shadow_ratio

        # 原始影线长度
        upper_shadow = high_price - max(open_price, close_price)
        lower_shadow = min(open_price, close_price) - low_price

        # 缩短上影线
        new_high = high_price
        if upper_shadow > max_shadow_length:
            new_high = max(open_price, close_price) + max_shadow_length
            shadow_shortened_count += 1

        # 缩短下影线
        new_low = low_price
        if lower_shadow > max_shadow_length:
            new_low = min(open_price, close_price) - max_shadow_length
            shadow_shortened_count += 1

        # 应用调整
        df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = new_high
        df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = new_low

    print(f"影线修复完成，共处理 {shadow_shortened_count} 个长影线")
    return df_optimized

def detect_oscillation_patterns(df, window=5, oscillation_threshold=0.005):
    """
    检测几分钟内反复上下震荡的K线模式
    Args:
        df: K线数据DataFrame
        window: 检测窗口大小（分钟数）
        oscillation_threshold: 震荡检测阈值（价格变化比例）
    Returns:
        震荡区间列表
    """
    oscillation_zones = []

    print(f"正在检测{window}分钟内的价格震荡模式...")

    for i in range(window, len(df) - window):
        # 获取窗口内的数据
        window_data = df.iloc[i-window:i+window+1]

        # 计算价格变化方向
        price_changes = []
        for j in range(1, len(window_data)):
            prev_close = window_data.iloc[j-1]['close']
            curr_close = window_data.iloc[j]['close']
            change = (curr_close - prev_close) / prev_close
            price_changes.append(1 if change > oscillation_threshold else -1 if change < -oscillation_threshold else 0)

        # 检测震荡模式：频繁的方向变化
        direction_changes = 0
        for j in range(1, len(price_changes)):
            if price_changes[j] != 0 and price_changes[j-1] != 0 and price_changes[j] != price_changes[j-1]:
                direction_changes += 1

        # 如果方向变化次数超过阈值，认为是震荡
        if direction_changes >= window // 2:
            # 计算震荡区间的价格范围
            window_high = window_data['high'].max()
            window_low = window_data['low'].min()
            window_range = (window_high - window_low) / window_low

            # 只处理显著的震荡（价格范围超过1%）
            if window_range > 0.01:
                oscillation_zones.append({
                    'start_idx': i - window,
                    'end_idx': i + window,
                    'center_price': (window_high + window_low) / 2,
                    'price_range': window_range,
                    'direction_changes': direction_changes
                })

    # 合并重叠的震荡区间
    merged_zones = []
    for zone in oscillation_zones:
        merged = False
        for existing_zone in merged_zones:
            if (zone['start_idx'] <= existing_zone['end_idx'] and
                zone['end_idx'] >= existing_zone['start_idx']):
                # 合并区间
                existing_zone['start_idx'] = min(existing_zone['start_idx'], zone['start_idx'])
                existing_zone['end_idx'] = max(existing_zone['end_idx'], zone['end_idx'])
                existing_zone['direction_changes'] = max(existing_zone['direction_changes'], zone['direction_changes'])
                merged = True
                break
        if not merged:
            merged_zones.append(zone)

    print(f"检测到 {len(merged_zones)} 个震荡区间")
    return merged_zones

def smooth_oscillation_zones(df, oscillation_zones, smoothing_strength=0.8):
    """
    对震荡区间进行平滑处理，消除反复上下震荡
    Args:
        df: K线数据DataFrame
        oscillation_zones: 震荡区间列表
        smoothing_strength: 平滑强度 (0-1之间，越大平滑程度越高)
    Returns:
        平滑后的DataFrame
    """
    df_smoothed = df.copy()
    total_smoothed = 0

    print(f"正在对震荡区间进行平滑处理，平滑强度: {smoothing_strength}")

    for zone in oscillation_zones:
        start_idx = zone['start_idx']
        end_idx = zone['end_idx']

        # 获取震荡区间的数据
        zone_data = df_smoothed.iloc[start_idx:end_idx+1].copy()

        if len(zone_data) < 3:
            continue

        # 计算区间的趋势线（线性回归）
        x = np.arange(len(zone_data))
        close_prices = zone_data['close'].values

        # 简单线性回归
        n = len(x)
        sum_x = np.sum(x)
        sum_y = np.sum(close_prices)
        sum_xy = np.sum(x * close_prices)
        sum_x2 = np.sum(x * x)

        # 计算斜率和截距
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        intercept = (sum_y - slope * sum_x) / n

        # 生成平滑的趋势价格
        trend_prices = intercept + slope * x

        # 应用平滑处理
        for i, idx in enumerate(range(start_idx, end_idx + 1)):
            original_close = df_smoothed.iloc[idx]['close']
            trend_close = trend_prices[i]

            # 混合原始价格和趋势价格
            smoothed_close = smoothing_strength * trend_close + (1 - smoothing_strength) * original_close

            # 计算调整比例
            if original_close > 0:
                adjustment_ratio = smoothed_close / original_close

                # 调整所有价格
                df_smoothed.iloc[idx, df_smoothed.columns.get_loc('open')] *= adjustment_ratio
                df_smoothed.iloc[idx, df_smoothed.columns.get_loc('high')] *= adjustment_ratio
                df_smoothed.iloc[idx, df_smoothed.columns.get_loc('low')] *= adjustment_ratio
                df_smoothed.iloc[idx, df_smoothed.columns.get_loc('close')] = smoothed_close

        total_smoothed += (end_idx - start_idx + 1)

        # 输出震荡区间信息
        start_time = datetime.fromtimestamp(df_smoothed.iloc[start_idx]['timestamp_sec'])
        end_time = datetime.fromtimestamp(df_smoothed.iloc[end_idx]['timestamp_sec'])
        print(f"  平滑震荡区间: {start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')} "
              f"({end_idx - start_idx + 1}根K线, 方向变化{zone['direction_changes']}次)")

    print(f"震荡平滑处理完成，共平滑 {total_smoothed} 根K线")
    return df_smoothed

def check_and_fill_gaps(df):
    """
    检查K线数据中的时间间隙并用插值填补
    Args:
        df: K线数据DataFrame
    Returns:
        完整的K线数据DataFrame
    """
    print("正在检查K线数据的时间连续性...")

    # 转换时间戳为datetime
    df_sorted = df.sort_values('timestamp_sec').reset_index(drop=True)

    # 检查时间间隙
    gaps = []
    expected_interval = 60  # 1分钟 = 60秒

    for i in range(1, len(df_sorted)):
        current_time = df_sorted['timestamp_sec'].iloc[i]
        prev_time = df_sorted['timestamp_sec'].iloc[i-1]
        time_diff = current_time - prev_time

        if time_diff > expected_interval:
            # 发现间隙
            gap_minutes = time_diff // 60 - 1
            gaps.append({
                'gap_start': prev_time + 60,
                'gap_end': current_time - 60,
                'gap_minutes': gap_minutes,
                'prev_close': df_sorted['close'].iloc[i-1],
                'next_open': df_sorted['open'].iloc[i]
            })

    if not gaps:
        print("✓ 未发现时间间隙，K线数据连续完整")
        return df

    print(f"发现 {len(gaps)} 个时间间隙，总计缺失 {sum(gap['gap_minutes'] for gap in gaps)} 分钟")

    # 创建完整的时间序列
    start_time = df_sorted['timestamp_sec'].min()
    end_time = df_sorted['timestamp_sec'].max()

    # 生成完整的分钟时间戳序列
    complete_timestamps = []
    current_timestamp = start_time
    while current_timestamp <= end_time:
        complete_timestamps.append(current_timestamp)
        current_timestamp += 60

    # 创建完整的DataFrame
    complete_df = pd.DataFrame({'timestamp_sec': complete_timestamps})

    # 合并原始数据
    merged_df = pd.merge(complete_df, df, on='timestamp_sec', how='left')

    # 对缺失的数据进行插值
    for col in ['open', 'close', 'high', 'low']:
        # 线性插值价格数据
        merged_df[col] = merged_df[col].interpolate(method='linear')

    # 对于交易量数据，使用相邻数据的平均值并缩放
    for col in ['amount', 'vol']:
        merged_df[col] = merged_df[col].fillna(0)

        # 对于连续的缺失段，使用相邻数据的平均值
        for gap in gaps:
            gap_start_ts = gap['gap_start']
            gap_end_ts = gap['gap_end']

            # 找到间隙前后的交易量
            prev_idx = merged_df[merged_df['timestamp_sec'] == gap_start_ts - 60].index
            next_idx = merged_df[merged_df['timestamp_sec'] == gap_end_ts + 60].index

            if len(prev_idx) > 0 and len(next_idx) > 0:
                prev_amount = merged_df.loc[prev_idx[0], 'amount']
                next_amount = merged_df.loc[next_idx[0], 'amount']
                prev_vol = merged_df.loc[prev_idx[0], 'vol']
                next_vol = merged_df.loc[next_idx[0], 'vol']

                # 计算平均值并分配给间隙中的K线
                avg_amount = (prev_amount + next_amount) / 2 * 0.1  # 减少到10%，因为是插值数据
                avg_vol = (prev_vol + next_vol) / 2 * 0.1

                # 填充间隙
                gap_mask = (merged_df['timestamp_sec'] >= gap_start_ts) & (merged_df['timestamp_sec'] <= gap_end_ts)
                merged_df.loc[gap_mask, 'amount'] = avg_amount
                merged_df.loc[gap_mask, 'vol'] = avg_vol

    # 确保OHLC逻辑正确
    for i in range(len(merged_df)):
        open_price = merged_df['open'].iloc[i]
        close_price = merged_df['close'].iloc[i]
        high_price = merged_df['high'].iloc[i]
        low_price = merged_df['low'].iloc[i]

        # 确保high >= max(open, close) 且 low <= min(open, close)
        corrected_high = max(high_price, open_price, close_price)
        corrected_low = min(low_price, open_price, close_price)

        merged_df.iloc[i, merged_df.columns.get_loc('high')] = corrected_high
        merged_df.iloc[i, merged_df.columns.get_loc('low')] = corrected_low

    # 重新排列列顺序
    final_df = merged_df[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()

    # 格式化数值
    for col in ['amount', 'vol', 'open', 'close', 'high', 'low']:
        final_df[col] = final_df[col].round(6)

    interpolated_count = len(final_df) - len(df)
    print(f"插值完成，共插值 {interpolated_count} 根K线")
    print(f"最终数据: {len(final_df)} 根K线，数据完整性: 100.0%")

    return final_df

def resample_kline_data(df_1min, timeframes=['5T', '15T', '30T', '1H', '1D']):
    """
    将1分钟K线数据重采样为多个时间周期
    Args:
        df_1min: 1分钟K线数据DataFrame
        timeframes: 时间周期列表，如['5T', '15T', '30T', '1H', '1D']
    Returns:
        dict: 包含各时间周期数据的字典
    """
    print("正在进行多时间周期重采样...")

    # 准备数据
    df_temp = df_1min.copy()
    df_temp['datetime'] = pd.to_datetime(df_temp['timestamp_sec'], unit='s')
    df_temp.set_index('datetime', inplace=True)

    resampled_data = {}
    timeframe_names = {
        '5T': '5分钟',
        '15T': '15分钟',
        '30T': '30分钟',
        '1H': '1小时',
        '1D': '1天'
    }

    for timeframe in timeframes:
        print(f"正在重采样{timeframe_names.get(timeframe, timeframe)}K线...")

        # 重采样OHLC数据
        resampled = df_temp.resample(timeframe).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'amount': 'sum',
            'vol': 'sum'
        }).dropna()

        # 重置索引并添加时间戳
        resampled.reset_index(inplace=True)
        resampled['timestamp_sec'] = resampled['datetime'].astype('int64') // 10**9

        # 重新排列列顺序
        resampled = resampled[['timestamp_sec', 'amount', 'vol', 'open', 'close', 'high', 'low']].copy()

        # 格式化数值
        for col in ['amount', 'vol', 'open', 'close', 'high', 'low']:
            resampled[col] = resampled[col].round(6)

        resampled_data[timeframe] = resampled

        # 保存到文件
        filename = f'bybusdt_{timeframe}_kline_2025-07-03.csv'
        resampled.to_csv(filename, index=False)
        print(f"  {timeframe_names.get(timeframe, timeframe)}K线数据已保存: {filename} ({len(resampled)}条)")

    print(f"多时间周期重采样完成，共生成{len(timeframes)}个时间周期的数据")
    return resampled_data

def detect_and_optimize_spikes(df, spike_threshold=2.5):
    """
    检测和优化插针
    Args:
        df: K线数据DataFrame
        spike_threshold: 插针检测阈值（标准差倍数）
    Returns:
        优化后的DataFrame和插针信息
    """
    df_optimized = df.copy()
    spike_info = []

    # 计算价格移动平均和标准差
    df_optimized['price_ma'] = df_optimized['close'].rolling(window=20, center=True).mean()
    df_optimized['price_std'] = df_optimized['close'].rolling(window=20, center=True).std()

    # 填充缺失值
    df_optimized['price_ma'].fillna(method='bfill', inplace=True)
    df_optimized['price_ma'].fillna(method='ffill', inplace=True)
    df_optimized['price_std'].fillna(df_optimized['price_std'].mean(), inplace=True)

    optimization_count = 0

    for i in range(len(df_optimized)):
        high_price = df_optimized['high'].iloc[i]
        low_price = df_optimized['low'].iloc[i]
        open_price = df_optimized['open'].iloc[i]
        close_price = df_optimized['close'].iloc[i]
        price_ma = df_optimized['price_ma'].iloc[i]
        price_std = df_optimized['price_std'].iloc[i]

        # 检测上插针
        upper_threshold = price_ma + spike_threshold * price_std
        if high_price > upper_threshold:
            # 优化上插针
            optimized_high = min(upper_threshold, max(open_price, close_price) * 1.02)
            df_optimized.iloc[i, df_optimized.columns.get_loc('high')] = optimized_high
            spike_info.append({
                'index': i,
                'type': '上插针',
                'original_high': high_price,
                'optimized_high': optimized_high,
                'reduction': high_price - optimized_high
            })
            optimization_count += 1

        # 检测下插针
        lower_threshold = price_ma - spike_threshold * price_std
        if low_price < lower_threshold:
            # 优化下插针
            optimized_low = max(lower_threshold, min(open_price, close_price) * 0.98)
            df_optimized.iloc[i, df_optimized.columns.get_loc('low')] = optimized_low
            spike_info.append({
                'index': i,
                'type': '下插针',
                'original_low': low_price,
                'optimized_low': optimized_low,
                'reduction': optimized_low - low_price
            })
            optimization_count += 1

    print(f"插针优化完成: 检测到{len(spike_info)}个插针，优化了{optimization_count}处")

    # 清理临时列
    df_optimized.drop(['price_ma', 'price_std'], axis=1, errors='ignore', inplace=True)

    return df_optimized, spike_info

def plot_candlestick_chart(df, title="BYBUSDT K线图", timeframe="1分钟", optimize_spikes=True):
    """
    绘制K线图
    Args:
        df: K线数据DataFrame
        title: 图表标题
        timeframe: 时间周期
        optimize_spikes: 是否优化插针
    """
    # 数据预处理
    df_plot = df.copy()

    # 转换时间戳为datetime
    df_plot['datetime'] = pd.to_datetime(df_plot['timestamp_sec'], unit='s')

    # 插针优化
    spike_info = []
    if optimize_spikes:
        print("正在检测和优化插针...")
        df_plot, spike_info = detect_and_optimize_spikes(df_plot)

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12),
                                   gridspec_kw={'height_ratios': [3, 1]})

    # 主图：K线图
    for i in range(len(df_plot)):
        row = df_plot.iloc[i]
        x = row['datetime']
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']

        # 确定颜色：红涨绿跌
        color = 'red' if close_price >= open_price else 'green'

        # 绘制影线
        ax1.plot([x, x], [low_price, high_price], color='black', linewidth=0.8)

        # 绘制实体
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)

        if body_height > 0:
            rect = Rectangle((mdates.date2num(x) - 0.0003, body_bottom),
                           0.0006, body_height,
                           facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
            ax1.add_patch(rect)
        else:
            # 十字星
            ax1.plot([mdates.date2num(x) - 0.0003, mdates.date2num(x) + 0.0003],
                    [open_price, open_price], color='black', linewidth=1)

    # 设置主图
    full_title = f"{title} ({timeframe})"
    if optimize_spikes and spike_info:
        full_title += f" - 已优化{len(spike_info)}个插针"

    ax1.set_title(full_title, fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('价格 (USDT)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax1.xaxis.set_major_locator(mdates.HourLocator(interval=2))

    # 添加价格统计信息
    price_info = f"开盘: {df_plot['open'].iloc[0]:.6f} | " \
                f"收盘: {df_plot['close'].iloc[-1]:.6f} | " \
                f"最高: {df_plot['high'].max():.6f} | " \
                f"最低: {df_plot['low'].min():.6f}"
    ax1.text(0.02, 0.98, price_info, transform=ax1.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
            verticalalignment='top', fontsize=10)

    # 副图：交易量
    colors = ['red' if df_plot.iloc[i]['close'] >= df_plot.iloc[i]['open']
              else 'green' for i in range(len(df_plot))]

    ax2.bar(df_plot['datetime'], df_plot['vol'],
           color=colors, alpha=0.7, width=pd.Timedelta(minutes=0.8))

    ax2.set_ylabel('交易量 (BYB)', fontsize=12)
    ax2.set_xlabel('时间', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    ax2.xaxis.set_major_locator(mdates.HourLocator(interval=2))

    # 旋转x轴标签
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    timeframe_safe = timeframe.replace(':', '').replace('/', '')
    filename = f'bybusdt_{timeframe_safe}_kline_chart_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"K线图已保存为: {filename}")

    # 显示图表
    plt.show()

    return df_plot, spike_info

def plot_multi_timeframe_comparison(resampled_data):
    """
    绘制多时间周期对比图
    Args:
        resampled_data: 包含多个时间周期数据的字典
    """
    timeframe_names = {
        '5T': '5分钟',
        '15T': '15分钟',
        '30T': '30分钟',
        '1H': '1小时',
        '1D': '1天'
    }

    # 创建子图
    fig, axes = plt.subplots(len(resampled_data), 1, figsize=(18, 4*len(resampled_data)),
                            sharex=True)

    if len(resampled_data) == 1:
        axes = [axes]

    for i, (timeframe, df) in enumerate(resampled_data.items()):
        ax = axes[i]

        # 转换时间
        df_plot = df.copy()
        df_plot['datetime'] = pd.to_datetime(df_plot['timestamp_sec'], unit='s')

        # 绘制收盘价线图
        ax.plot(df_plot['datetime'], df_plot['close'], linewidth=2,
               label=f'{timeframe_names.get(timeframe, timeframe)}收盘价')

        # 填充高低价区域
        ax.fill_between(df_plot['datetime'], df_plot['low'], df_plot['high'],
                       alpha=0.3, label=f'{timeframe_names.get(timeframe, timeframe)}价格区间')

        ax.set_title(f'{timeframe_names.get(timeframe, timeframe)}K线价格走势',
                    fontsize=14, fontweight='bold')
        ax.set_ylabel('价格 (USDT)', fontsize=12)
        ax.grid(True, alpha=0.3)
        ax.legend()

        # 格式化x轴
        if timeframe in ['1H', '1D']:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
        else:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))

    # 设置最后一个子图的x轴标签
    axes[-1].set_xlabel('时间', fontsize=12)
    plt.setp(axes[-1].xaxis.get_majorticklabels(), rotation=45)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    filename = f'bybusdt_multi_timeframe_comparison_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"多时间周期对比图已保存为: {filename}")

    # 显示图表
    plt.show()

    return filename

def analyze_kline_data(df, timeframe="1分钟"):
    """分析K线数据并输出统计信息"""
    print(f"\n=== BYBUSDT {timeframe}K线数据分析 ===")
    print(f"数据时间范围: {datetime.fromtimestamp(df['timestamp_sec'].min())} 到 {datetime.fromtimestamp(df['timestamp_sec'].max())}")
    print(f"K线总数: {len(df)} 根")

    # 价格统计
    print(f"\n价格统计:")
    print(f"开盘价: {df['open'].iloc[0]:.6f} USDT")
    print(f"收盘价: {df['close'].iloc[-1]:.6f} USDT")
    print(f"最高价: {df['high'].max():.6f} USDT")
    print(f"最低价: {df['low'].min():.6f} USDT")
    print(f"价格振幅: {((df['high'].max() - df['low'].min()) / df['low'].min() * 100):.2f}%")

    # 涨跌统计
    up_count = (df['close'] > df['open']).sum()
    down_count = (df['close'] < df['open']).sum()
    flat_count = (df['close'] == df['open']).sum()

    print(f"\n涨跌统计:")
    print(f"上涨K线: {up_count} 根 ({up_count/len(df)*100:.1f}%)")
    print(f"下跌K线: {down_count} 根 ({down_count/len(df)*100:.1f}%)")
    print(f"平盘K线: {flat_count} 根 ({flat_count/len(df)*100:.1f}%)")

    # 交易量统计
    print(f"\n交易量统计:")
    print(f"总交易额: {df['amount'].sum():.2f} USDT")
    print(f"总交易量: {df['vol'].sum():.2f} BYB")
    print(f"平均交易额: {df['amount'].mean():.2f} USDT")
    print(f"平均交易量: {df['vol'].mean():.2f} BYB")

    # 波动率统计 (使用high/low-1公式)
    volatilities = []
    for i in range(len(df)):
        if df['low'].iloc[i] > 0:
            vol = df['high'].iloc[i] / df['low'].iloc[i] - 1
            volatilities.append(vol)

    if volatilities:
        avg_volatility = np.mean(volatilities)
        max_volatility = max(volatilities)
        over_1pct = sum(1 for vol in volatilities if vol > 0.01)
        over_2pct = sum(1 for vol in volatilities if vol > 0.02)

        print(f"\n波动率统计 (high/low-1):")
        print(f"平均波动率: {avg_volatility:.4f} ({avg_volatility*100:.2f}%)")
        print(f"最大波动率: {max_volatility:.4f} ({max_volatility*100:.2f}%)")
        print(f"超过1%波动的K线: {over_1pct} 根 ({over_1pct/len(df)*100:.1f}%)")
        print(f"超过2%波动的K线: {over_2pct} 根 ({over_2pct/len(df)*100:.1f}%)")

def generate_comprehensive_report(df_original, df_optimized, resampled_data):
    """生成综合分析报告"""
    print("\n" + "="*80)
    print("BYBUSDT K线数据综合分析报告")
    print("="*80)

    # 分析原始数据
    print("\n=== 原始数据分析 ===")
    analyze_kline_data(df_original, "1分钟（原始）")

    # 分析优化后数据
    print("\n=== 优化后数据分析 ===")
    analyze_kline_data(df_optimized, "1分钟（优化后）")

    # 优化效果对比
    print("\n=== 优化效果对比 ===")

    # 计算波动率对比
    original_volatilities = []
    optimized_volatilities = []

    for i in range(len(df_original)):
        # 原始波动率
        if df_original['low'].iloc[i] > 0:
            orig_vol = df_original['high'].iloc[i] / df_original['low'].iloc[i] - 1
            original_volatilities.append(orig_vol)

        # 优化后波动率
        if df_optimized['low'].iloc[i] > 0:
            opt_vol = df_optimized['high'].iloc[i] / df_optimized['low'].iloc[i] - 1
            optimized_volatilities.append(opt_vol)

    if original_volatilities and optimized_volatilities:
        avg_orig_vol = np.mean(original_volatilities)
        avg_opt_vol = np.mean(optimized_volatilities)
        max_orig_vol = max(original_volatilities)
        max_opt_vol = max(optimized_volatilities)

        orig_over_1pct = sum(1 for vol in original_volatilities if vol > 0.01)
        opt_over_1pct = sum(1 for vol in optimized_volatilities if vol > 0.01)

        print(f"波动率优化效果:")
        print(f"  平均波动率: {avg_orig_vol:.4f} → {avg_opt_vol:.4f} (降低 {(avg_orig_vol-avg_opt_vol)/avg_orig_vol*100:.1f}%)")
        print(f"  最大波动率: {max_orig_vol:.4f} → {max_opt_vol:.4f} (降低 {(max_orig_vol-max_opt_vol)/max_orig_vol*100:.1f}%)")
        print(f"  超过1%波动的K线: {orig_over_1pct} → {opt_over_1pct} (减少 {orig_over_1pct-opt_over_1pct} 根)")

    # 价格范围对比
    orig_price_range = df_original['high'].max() - df_original['low'].min()
    opt_price_range = df_optimized['high'].max() - df_optimized['low'].min()

    print(f"\n价格范围优化:")
    print(f"  总价格范围: {orig_price_range:.6f} → {opt_price_range:.6f} USDT")
    print(f"  范围变化: {((opt_price_range-orig_price_range)/orig_price_range*100):+.2f}%")

    # 分析各时间周期数据
    timeframe_names = {
        '5T': '5分钟',
        '15T': '15分钟',
        '30T': '30分钟',
        '1H': '1小时',
        '1D': '1天'
    }

    for timeframe, df in resampled_data.items():
        analyze_kline_data(df, timeframe_names.get(timeframe, timeframe))

    # 生成数据质量评估
    print(f"\n=== 数据质量评估 ===")

    # 检查1分钟数据质量
    volatilities_1min = []
    for i in range(len(df_optimized)):
        if df_optimized['low'].iloc[i] > 0:
            vol = df_optimized['high'].iloc[i] / df_optimized['low'].iloc[i] - 1
            volatilities_1min.append(vol)

    large_moves_1min = sum(1 for vol in volatilities_1min if vol > 0.02)
    quality_score = max(0, 100 - (large_moves_1min / len(df_optimized) * 100 * 10))

    print(f"1分钟数据质量评分: {quality_score:.1f}/100")
    if quality_score >= 90:
        print("✓ 数据质量优秀，适合技术分析")
    elif quality_score >= 70:
        print("⚠ 数据质量良好，可用于分析")
    else:
        print("⚠ 数据质量一般，建议进一步优化")

    # 保存报告到文件
    report_filename = f'bybusdt_kline_analysis_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("BYBUSDT K线数据综合分析报告\n")
        f.write("="*50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 写入基本统计信息
        f.write("基本统计信息:\n")
        f.write(f"1分钟K线数量: {len(df_optimized)} 根\n")
        f.write(f"时间跨度: {(df_optimized['timestamp_sec'].max() - df_optimized['timestamp_sec'].min()) / 3600:.1f} 小时\n")
        f.write(f"价格范围: {df_optimized['low'].min():.6f} - {df_optimized['high'].max():.6f} USDT\n")
        f.write(f"总交易额: {df_optimized['amount'].sum():.2f} USDT\n")
        f.write(f"总交易量: {df_optimized['vol'].sum():.2f} BYB\n\n")

        # 写入各时间周期统计
        for timeframe, df in resampled_data.items():
            f.write(f"{timeframe_names.get(timeframe, timeframe)}K线统计:\n")
            f.write(f"  数量: {len(df)} 根\n")
            f.write(f"  价格范围: {df['low'].min():.6f} - {df['high'].max():.6f} USDT\n")
            f.write(f"  总交易额: {df['amount'].sum():.2f} USDT\n\n")

        f.write(f"数据质量评分: {quality_score:.1f}/100\n")

    print(f"\n分析报告已保存为: {report_filename}")
    return report_filename

def main():
    """主函数 - 完整的K线数据处理流程"""
    print("="*80)
    print("BYBUSDT K线数据完整处理系统")
    print("="*80)
    print("功能包括:")
    print("1. 从Excel交易数据生成1分钟K线")
    print("2. 价格异常检测（开盘跳空、极端波动、收盘价异常）")
    print("3. 极端跳空优化（限制跳空幅度）")
    print("4. 超严格价格平滑（强力平滑算法）")
    print("5. OHLC平滑优化处理")
    print("6. 影线修复（缩短长影线）")
    print("7. 震荡检测和平滑处理（消除反复上下震荡）")
    print("8. 多时间周期重采样 (5分钟、15分钟、30分钟、1小时、1天)")
    print("9. 插针检测和优化")
    print("10. K线图表绘制")
    print("11. 多时间周期对比图")
    print("12. 综合数据分析报告")
    print("="*80)

    try:
        # 第一步：生成1分钟K线数据
        print("\n第一步：生成1分钟K线数据...")
        df_1min = process_bybusdt_trades_to_kline()

        # 保存原始数据用于对比
        df_original = df_1min.copy()

        if df_1min is None or len(df_1min) == 0:
            print("错误：未能生成1分钟K线数据")
            return

        # 第二步：多时间周期重采样
        print("\n第二步：多时间周期重采样...")
        timeframes = ['5T', '15T', '30T', '1H', '1D']
        resampled_data = resample_kline_data(df_1min, timeframes)

        # 第三步：绘制1分钟K线图
        print("\n第三步：绘制1分钟K线图...")
        df_1min_optimized, spike_info = plot_candlestick_chart(
            df_1min,
            title="BYBUSDT K线图",
            timeframe="1分钟",
            optimize_spikes=True
        )

        # 第四步：绘制多时间周期对比图
        print("\n第四步：绘制多时间周期对比图...")
        comparison_chart = plot_multi_timeframe_comparison(resampled_data)

        # 第五步：生成综合分析报告
        print("\n第五步：生成综合分析报告...")
        report_file = generate_comprehensive_report(df_original, df_1min, resampled_data)

        # 总结
        print("\n" + "="*80)
        print("处理完成！生成的文件:")
        print("="*80)
        print("K线数据文件:")
        print("  - bybusdt_1min_kline_2025-07-03.csv (1分钟)")
        for timeframe in timeframes:
            timeframe_name = {'5T': '5分钟', '15T': '15分钟', '30T': '30分钟', '1H': '1小时', '1D': '1天'}[timeframe]
            print(f"  - bybusdt_{timeframe}_kline_2025-07-03.csv ({timeframe_name})")

        print("\n图表文件:")
        print("  - bybusdt_1分钟_kline_chart_*.png (1分钟K线图)")
        print(f"  - {comparison_chart} (多时间周期对比图)")

        print(f"\n分析报告:")
        print(f"  - {report_file}")

        if spike_info:
            print(f"\n插针优化:")
            print(f"  - 检测并优化了 {len(spike_info)} 个插针")

        print("\n🎉 BYBUSDT K线数据处理系统运行完成！")

    except FileNotFoundError:
        print("错误: 未找到Excel文件 '用户实时交易单2025-07-03-2025-07-03.xls'")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()